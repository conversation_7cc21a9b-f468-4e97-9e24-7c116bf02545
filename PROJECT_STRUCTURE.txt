NuclearStory/
├── README.md
├── QUICK-START-WINDOWS.md
├── PROJECT_STRUCTURE.txt
│
├── ai/
│   ├── README.md
│   ├── ai-service/
│   │   ├── Dockerfile
│   │   ├── main.py
│   │   ├── requirements.txt
│   │   ├── test_ai.py
│   │   └── __pycache__/
│   ├── integrations/
│   ├── models/
│   ├── prompts/
│   │   └── story-generation.md
│   └── utils/
│
├── back/
│   ├── README.md
│   ├── auth-service/
│   │   ├── Dockerfile
│   │   ├── Dockerfile.dev
│   │   ├── nest-cli.json
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   ├── dist/
│   │   ├── node_modules/
│   │   └── src/
│   │       ├── app.module.ts
│   │       ├── main.ts
│   │       ├── auth/
│   │       │   ├── auth.controller.ts
│   │       │   ├── auth.module.ts
│   │       │   ├── auth.service.ts
│   │       │   ├── decorators/
│   │       │   ├── dto/
│   │       │   ├── guards/
│   │       │   └── strategies/
│   │       ├── database/
│   │       │   └── database.module.ts
│   │       └── users/
│   │           ├── users.module.ts
│   │           ├── users.service.ts
│   │           ├── dto/
│   │           └── entities/
│   ├── game-engine-service/
│   │   ├── Dockerfile
│   │   ├── Dockerfile.dev
│   │   ├── nest-cli.json
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   ├── dist/
│   │   ├── node_modules/
│   │   └── src/
│   ├── save-service/
│   │   ├── Dockerfile
│   │   ├── Dockerfile.dev
│   │   ├── nest-cli.json
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   ├── dist/
│   │   ├── node_modules/
│   │   └── src/
│   └── story-service/
│       ├── Dockerfile
│       ├── Dockerfile.dev
│       ├── nest-cli.json
│       ├── package.json
│       ├── tsconfig.json
│       ├── dist/
│       ├── node_modules/
│       └── src/
│
├── front/
│   ├── Dockerfile
│   ├── Dockerfile.dev
│   ├── README.md
│   ├── index.html
│   ├── nginx.conf
│   ├── package.json
│   ├── package-lock.json
│   ├── postcss.config.js
│   ├── tailwind.config.js
│   ├── tsconfig.json
│   ├── tsconfig.node.json
│   ├── vite.config.ts
│   ├── .env
│   ├── dist/
│   ├── node_modules/
│   ├── public/
│   └── src/
│       ├── App.tsx
│       ├── main.tsx
│       ├── vite-env.d.ts
│       ├── components/
│       │   ├── Header.tsx
│       │   └── Layout.tsx
│       ├── hooks/
│       ├── pages/
│       │   ├── GamePage.tsx
│       │   ├── HomePage.tsx
│       │   ├── LoginPage.tsx
│       │   └── SignupPage.tsx
│       ├── services/
│       ├── store/
│       │   └── authStore.tsx
│       ├── styles/
│       │   └── globals.css
│       ├── types/
│       │   ├── global.d.ts
│       │   └── react.d.ts
│       └── utils/
│
├── infra/
│   ├── README.md
│   ├── WINDOWS-SETUP.md
│   ├── docker-compose.yml
│   ├── docker-compose.dev.yml
│   ├── start-dev.bat
│   ├── stop-dev.bat
│   ├── test-system.bat
│   ├── nginx/
│   │   └── nginx.conf
│   ├── postgres/
│   │   ├── init-auth.sql
│   │   └── init-saves.sql
│   └── scripts/
│       ├── build.sh
│       ├── check-docker.sh
│       ├── dev-logs.ps1
│       ├── dev-logs.sh
│       ├── dev-rebuild.sh
│       ├── dev-start.ps1
│       ├── dev-start.sh
│       ├── dev-stop.ps1
│       ├── dev-stop.sh
│       ├── dev.sh
│       ├── fix-typescript.ps1
│       ├── quick-start.sh
│       ├── start.sh
│       ├── status.ps1
│       ├── status.sh
│       └── stop.sh
│
├── shared/
│   ├── README.md
│   ├── package.json
│   ├── tsconfig.json
│   └── src/
│       ├── index.ts
│       ├── enums/
│       │   └── index.ts
│       ├── models/
│       │   ├── InventoryItem.ts
│       │   ├── Quest.ts
│       │   ├── SaveData.ts
│       │   └── User.ts
│       ├── types/
│       │   └── index.ts
│       └── utils/
│           └── index.ts
│
└── docs/
    └── README.md




Корень проекта /

    README.md — краткое описание проекта, как запускать, стек

    QUICK-START-WINDOWS.md — быстрая инструкция по запуску в Windows

    PROJECT_STRUCTURE.txt — справка по структуре проекта (то, что ты сейчас делаешь)

—

📁 ai/

Цель: всё, что связано с AI-генерацией (OpenAI, Hugging Face и т.п.)

    ai-service/ — основной AI-сервис на Python или Node, оборачивает запросы к LLM

        Dockerfile — для сборки образа AI-сервиса

        main.py — стартовый файл сервиса

        requirements.txt — зависимости Python

        test_ai.py — модульные тесты

    integrations/ — обёртки под конкретные LLM API (OpenAI, Ollama, Local)

    models/ — кастомные локальные модели, если будут

    prompts/ — шаблоны запросов к LLM (для генерации локаций, фракций и т.п.)

        story-generation.md — описание промптов, примеры

    utils/ — утилиты: форматирование, логгеры, pre/post обработка запросов

💡 AI будет вызываться из story-service через HTTP или через очередь.

—

📁 back/

Цель: микросервисный backend на NestJS (по папке на сервис)

    auth-service/ — NestJS сервис: регистрация, логин, JWT, пользователи

        /src/auth — логика авторизации

        /src/users — управление пользователями

        /src/database — подключение к базе (PostgreSQL)

    game-engine-service/ — логика карты, персонажа, боёв, передвижения, бросков

        /src/

            /player — текущие статы, координаты, level up

            /map — генерация карты, перемещения, доступные действия

            /battle — логика боя, сетки, боевые юниты

            /logic — броски кубов, рассчёты

    save-service/ — сохранения, загрузка прогресса игрока

        /src/

            /saves — логика создания, получения и версионирования сохранений

    story-service/ — генерация мира через AI, хранение сгенерированных фракций, событий и т.д.

        /src/

            /prompt — сборка промптов

            /story — фракции, описание локаций, NPC

            /dialogues — готовые сгенерированные диалоги

💡 Каждый сервис можно развивать отдельно, но все используют shared/модели.

—

📁 front/

Цель: фронтенд-часть (React + Vite)

    index.html — корневой HTML-шаблон

    nginx.conf — если нужен NGINX для сборки

    .env — конфиги (API_URL и т.д.)

    /src/

        App.tsx — основной компонент

        main.tsx — точка входа

        /components/ — общие компоненты UI

        /pages/ — отдельные страницы (игра, логин, старт)

        /store/ — Zustand-стейт менеджер (например, authStore.tsx)

        /services/ — API-запросы к микросервисам (auth, game, map)

        /hooks/ — кастомные хуки (например, useGameLoop)

        /types/ — глобальные TS типы

        /utils/ — утилиты: таймеры, diceRoll, рандом

        /styles/ — Tailwind + custom CSS

—

📁 infra/

Цель: инфраструктура проекта (Docker, Compose, скрипты, CI)

    docker-compose.yml — основной конфиг прод/тест сборки всех сервисов

    docker-compose.dev.yml — dev-версия с volume’ами и live-reload

    start-dev.bat / stop-dev.bat — удобный запуск под Windows

    nginx/ — конфиг NGINX (если фронт через него)

    postgres/

        init-auth.sql — создание юзеров, схемы и миграции под auth

        init-saves.sql — аналогично для save-service

    scripts/ — удобные утилиты и DevOps-команды (bash/powershell)

        dev-start.sh, dev-stop.sh — запуск/остановка dev-среды

        status.sh — показать статус контейнеров

        fix-typescript.ps1 — быстрое исправление common ошибок TS

—

📁 shared/

Цель: общие типы и модели, переиспользуемые фронтом и бэком

    /src/

        index.ts — реэкспорт

        /enums/ — перечисления (тип локации, тип врага и т.д.)

        /models/

            InventoryItem.ts — описание предмета

            Quest.ts — структура квеста

            SaveData.ts — модель сохранения

            User.ts — данные игрока

        /types/ — глобальные типы (NPC, Location, Cell, Weapon, BattleArena и т.д.)

        /utils/ — вспомогательные функции, общие для сервисов

💡 Это можно подключать в каждом сервисе как npm-пакет (через Yarn Workspaces / TS paths).

—

📁 docs/

Цель: справки, документация, схемы, mindmaps, планы

    README.md — можно описать фичи, этапы, диаграммы

    Также можно добавить:

        game-design.md — механики

        prompts.md — описание структуры prompt’ов

        story-template.md — шаблон фракции/локации

        tech.md — стек, зависимости, фреймворки

        roadmap.md — план на релизы (v0.1 → v1.0)

—

🧠 Резюме: что где создавать

    game logic: back/game-engine-service/src/...

    карта, перемещение, бой: game-engine-service/src/map/, battle/

    сохранения: save-service/src/saves/

    генерация мира: story-service/src/story/, /prompt/

    генерация через AI: ai-service + story-service

    API: каждый микросервис даёт свой REST или gRPC/gateway

    shared TS-модели: в shared/src/models/

    визуализация карты, интерфейс боя: front/src/pages/GamePage.tsx + components

—