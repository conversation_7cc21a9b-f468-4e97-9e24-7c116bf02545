import { useGameStore } from '../store/gameStore'

const GameInventory = () => {
  const { inventory, removeFromInventory, updateStats } = useGameStore()

  const getItemIcon = (item: string) => {
    const icons: Record<string, string> = {
      water_bottle: '💧',
      canned_food: '🥫',
      medkit: '🏥',
      rad_pills: '💊',
      energy_drink: '⚡',
      weapon: '🔫',
      ammo: '🔫',
      flashlight: '🔦',
      battery: '🔋',
      map: '🗺️',
    }
    return icons[item] || '📦'
  }

  const getItemName = (item: string) => {
    return item.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const useItem = (item: string) => {
    switch (item) {
      case 'water_bottle':
        updateStats({ thirst: Math.min(100, 100) })
        removeFromInventory(item)
        break
      case 'canned_food':
        updateStats({ hunger: Math.min(100, 100) })
        removeFromInventory(item)
        break
      case 'medkit':
        updateStats({ health: Math.min(100, 100) })
        removeFromInventory(item)
        break
      case 'rad_pills':
        updateStats({ radiation: Math.max(0, 0) })
        removeFromInventory(item)
        break
      case 'energy_drink':
        updateStats({ energy: Math.min(100, 100) })
        removeFromInventory(item)
        break
      default:
        console.log(`Used ${item}`)
    }
  }

  const canUseItem = (item: string) => {
    return ['water_bottle', 'canned_food', 'medkit', 'rad_pills', 'energy_drink'].includes(item)
  }

  return (
    <div className="bg-card p-6 rounded-lg border">
      <h3 className="text-lg font-semibold mb-4 text-primary">Inventory</h3>
      
      {inventory.length === 0 ? (
        <p className="text-muted-foreground text-sm">Your inventory is empty</p>
      ) : (
        <div className="space-y-2">
          {inventory.map((item, index) => (
            <div 
              key={`${item}-${index}`}
              className="flex items-center justify-between p-2 bg-muted rounded-md"
            >
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getItemIcon(item)}</span>
                <span className="text-sm  text-white font-medium">{getItemName(item)}</span>
              </div>
              
              {canUseItem(item) && (
                <button
                  onClick={() => useItem(item)}
                  className="px-2 py-1 bg-primary text-primary-foreground text-xs rounded hover:bg-primary/90 transition-colors"
                >
                  Use
                </button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default GameInventory
