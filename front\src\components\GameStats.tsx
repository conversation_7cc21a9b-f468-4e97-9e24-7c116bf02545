import { useGameStore } from '../store/gameStore'

const GameStats = () => {
  const { gameStats } = useGameStore()

  const getStatColor = (value: number, isRadiation = false) => {
    if (isRadiation) {
      if (value >= 80) return 'text-destructive'
      if (value >= 50) return 'text-yellow-500'
      return 'text-green-500'
    }
    
    if (value >= 70) return 'text-green-500'
    if (value >= 40) return 'text-yellow-500'
    return 'text-destructive'
  }

  const getStatBar = (value: number, isRadiation = false) => {
    const percentage = Math.max(0, Math.min(100, value))
    let bgColor = 'bg-green-500'
    
    if (isRadiation) {
      if (value >= 80) bgColor = 'bg-destructive'
      else if (value >= 50) bgColor = 'bg-yellow-500'
      else bgColor = 'bg-green-500'
    } else {
      if (value >= 70) bgColor = 'bg-green-500'
      else if (value >= 40) bgColor = 'bg-yellow-500'
      else bgColor = 'bg-destructive'
    }

    return (
      <div className="w-full bg-muted rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${bgColor}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    )
  }

  return (
    <div className="bg-card p-6 rounded-lg border">
      <h3 className="text-lg font-semibold mb-4 text-primary">Survival Stats</h3>
      
      <div className="space-y-4">
        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium">❤️ Health</span>
            <span className={`text-sm font-bold ${getStatColor(gameStats.health)}`}>
              {gameStats.health}%
            </span>
          </div>
          {getStatBar(gameStats.health)}
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium">🍖 Hunger</span>
            <span className={`text-sm font-bold ${getStatColor(gameStats.hunger)}`}>
              {gameStats.hunger}%
            </span>
          </div>
          {getStatBar(gameStats.hunger)}
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium">💧 Thirst</span>
            <span className={`text-sm font-bold ${getStatColor(gameStats.thirst)}`}>
              {gameStats.thirst}%
            </span>
          </div>
          {getStatBar(gameStats.thirst)}
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium">☢️ Radiation</span>
            <span className={`text-sm font-bold ${getStatColor(gameStats.radiation, true)}`}>
              {gameStats.radiation}%
            </span>
          </div>
          {getStatBar(gameStats.radiation, true)}
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium">⚡ Energy</span>
            <span className={`text-sm font-bold ${getStatColor(gameStats.energy)}`}>
              {gameStats.energy}%
            </span>
          </div>
          {getStatBar(gameStats.energy)}
        </div>
      </div>
    </div>
  )
}

export default GameStats
