import { useState } from 'react'
import { useGameStore } from '../store/gameStore'

const GameStory = () => {
  const { currentLocation, setLocation, addToInventory, updateStats } = useGameStore()
  const [currentStory, setCurrentStory] = useState(getLocationStory(currentLocation))

  function getLocationStory(location: string) {
    const stories: Record<string, any> = {
      bunker: {
        title: "Underground Bunker",
        text: "You find yourself in a dimly lit underground bunker. The air is stale but breathable. Emergency lights flicker overhead, casting eerie shadows on the concrete walls. You notice a supply cabinet in the corner and a heavy metal door leading outside.",
        choices: [
          {
            text: "Search the supply cabinet",
            action: () => {
              addToInventory('medkit')
              addToInventory('flashlight')
              setCurrentStory({
                title: "Supply Cabinet",
                text: "You find a medical kit and a working flashlight. These could be useful in the wasteland above.",
                choices: [
                  {
                    text: "Head to the exit",
                    action: () => {
                      setLocation('wasteland_entrance')
                      setCurrentStory(getLocationStory('wasteland_entrance'))
                    }
                  }
                ]
              })
            }
          },
          {
            text: "Go directly to the exit",
            action: () => {
              setLocation('wasteland_entrance')
              setCurrentStory(getLocationStory('wasteland_entrance'))
            }
          }
        ]
      },
      wasteland_entrance: {
        title: "Wasteland Entrance",
        text: "You push open the heavy bunker door and step into the post-apocalyptic world. The sky is a sickly yellow color, and the air tastes of metal and ash. Geiger counter readings show moderate radiation levels. You can see a ruined city in the distance and what appears to be an abandoned gas station nearby.",
        choices: [
          {
            text: "Head towards the ruined city",
            action: () => {
              updateStats({ radiation: 15, energy: -10 })
              setLocation('ruined_city')
              setCurrentStory(getLocationStory('ruined_city'))
            }
          },
          {
            text: "Investigate the gas station",
            action: () => {
              updateStats({ radiation: 5, energy: -5 })
              setLocation('gas_station')
              setCurrentStory(getLocationStory('gas_station'))
            }
          },
          {
            text: "Return to the bunker",
            action: () => {
              setLocation('bunker')
              setCurrentStory(getLocationStory('bunker'))
            }
          }
        ]
      },
      gas_station: {
        title: "Abandoned Gas Station",
        text: "The gas station is eerily quiet. Broken glass crunches under your feet as you approach. The pumps are destroyed, but the small convenience store might still have supplies. You notice movement in the shadows - could be dangerous.",
        choices: [
          {
            text: "Search the convenience store",
            action: () => {
              addToInventory('canned_food')
              addToInventory('water_bottle')
              updateStats({ radiation: 5 })
              setCurrentStory({
                title: "Convenience Store",
                text: "You find some canned food and a bottle of water. The radiation levels are slightly higher here, but the supplies are worth it.",
                choices: [
                  {
                    text: "Continue exploring",
                    action: () => {
                      setLocation('wasteland_entrance')
                      setCurrentStory(getLocationStory('wasteland_entrance'))
                    }
                  }
                ]
              })
            }
          },
          {
            text: "Avoid the area and leave",
            action: () => {
              setLocation('wasteland_entrance')
              setCurrentStory(getLocationStory('wasteland_entrance'))
            }
          }
        ]
      },
      ruined_city: {
        title: "Ruined City Outskirts",
        text: "The city is a graveyard of twisted metal and collapsed buildings. Radiation levels are dangerously high here. You can see what might be a hospital in the distance, and there's a subway entrance nearby that could provide shelter.",
        choices: [
          {
            text: "Risk going to the hospital",
            action: () => {
              updateStats({ radiation: 25, energy: -15 })
              addToInventory('medkit')
              addToInventory('rad_pills')
              setCurrentStory({
                title: "Abandoned Hospital",
                text: "The hospital is heavily irradiated but you manage to find medical supplies. Your radiation exposure is concerning.",
                choices: [
                  {
                    text: "Return to safer areas",
                    action: () => {
                      setLocation('wasteland_entrance')
                      setCurrentStory(getLocationStory('wasteland_entrance'))
                    }
                  }
                ]
              })
            }
          },
          {
            text: "Take shelter in the subway",
            action: () => {
              updateStats({ radiation: 10, energy: -5 })
              setLocation('subway')
              setCurrentStory(getLocationStory('subway'))
            }
          }
        ]
      },
      subway: {
        title: "Underground Subway",
        text: "The subway tunnels provide some protection from radiation. It's dark and damp, but relatively safe. You hear echoes in the distance - you might not be alone down here.",
        choices: [
          {
            text: "Explore deeper into the tunnels",
            action: () => {
              updateStats({ energy: -10 })
              addToInventory('battery')
              setCurrentStory({
                title: "Deep Tunnels",
                text: "You find some useful batteries but the tunnels seem to go on forever. It's time to head back.",
                choices: [
                  {
                    text: "Return to the surface",
                    action: () => {
                      setLocation('ruined_city')
                      setCurrentStory(getLocationStory('ruined_city'))
                    }
                  }
                ]
              })
            }
          },
          {
            text: "Return to the surface",
            action: () => {
              setLocation('ruined_city')
              setCurrentStory(getLocationStory('ruined_city'))
            }
          }
        ]
      }
    }
    
    return stories[location] || stories.bunker
  }

  return (
    <div className="bg-card p-6 rounded-lg border">
      <h2 className="text-2xl font-bold mb-4 text-primary">{currentStory.title}</h2>
      
      <div className="prose prose-invert max-w-none mb-6">
        <p className="text-foreground leading-relaxed">{currentStory.text}</p>
      </div>
      
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-primary">What do you do?</h3>
        {currentStory.choices.map((choice: any, index: number) => (
          <button
            key={index}
            onClick={choice.action}
            className="block w-full text-left p-4 bg-secondary hover:bg-secondary/80 rounded-lg transition-colors border border-border"
          >
            <span className="font-medium">{choice.text}</span>
          </button>
        ))}
      </div>
    </div>
  )
}

export default GameStory
