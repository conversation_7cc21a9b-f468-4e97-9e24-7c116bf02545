import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface GameStats {
  health: number
  hunger: number
  thirst: number
  radiation: number
  energy: number
}

interface GameState {
  // Game state
  isGameActive: boolean
  currentLocation: string
  gameStats: GameStats
  inventory: string[]
  currentQuest: string | null
  
  // Actions
  startGame: () => void
  endGame: () => void
  updateStats: (stats: Partial<GameStats>) => void
  addToInventory: (item: string) => void
  removeFromInventory: (item: string) => void
  setLocation: (location: string) => void
  setQuest: (quest: string | null) => void
  
  // Save/Load
  saveGame: () => void
  loadGame: () => void
}

export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      // Initial state
      isGameActive: false,
      currentLocation: 'bunker',
      gameStats: {
        health: 100,
        hunger: 100,
        thirst: 100,
        radiation: 0,
        energy: 100,
      },
      inventory: [],
      currentQuest: null,

      // Actions
      startGame: () => {
        set({
          isGameActive: true,
          currentLocation: 'bunker',
          gameStats: {
            health: 100,
            hunger: 100,
            thirst: 100,
            radiation: 0,
            energy: 100,
          },
          inventory: ['water_bottle', 'canned_food'],
          currentQuest: 'explore_wasteland',
        })
      },

      endGame: () => {
        set({
          isGameActive: false,
          currentQuest: null,
        })
      },

      updateStats: (newStats: Partial<GameStats>) => {
        const currentStats = get().gameStats
        set({
          gameStats: {
            ...currentStats,
            ...newStats,
          },
        })
      },

      addToInventory: (item: string) => {
        const currentInventory = get().inventory
        if (!currentInventory.includes(item)) {
          set({
            inventory: [...currentInventory, item],
          })
        }
      },

      removeFromInventory: (item: string) => {
        const currentInventory = get().inventory
        set({
          inventory: currentInventory.filter(i => i !== item),
        })
      },

      setLocation: (location: string) => {
        set({ currentLocation: location })
      },

      setQuest: (quest: string | null) => {
        set({ currentQuest: quest })
      },

      saveGame: () => {
        // TODO: Send game state to save-service
        console.log('Game saved:', get())
      },

      loadGame: () => {
        // TODO: Load game state from save-service
        console.log('Game loaded')
      },
    }),
    {
      name: 'game-storage',
      partialize: (state) => ({
        currentLocation: state.currentLocation,
        gameStats: state.gameStats,
        inventory: state.inventory,
        currentQuest: state.currentQuest,
      }),
    }
  )
)
