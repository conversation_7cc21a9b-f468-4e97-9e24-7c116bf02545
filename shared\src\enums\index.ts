export enum UserRole {
  PLAYER = 'player',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

export enum GameStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  GAME_OVER = 'game_over'
}

export enum QuestStatus {
  AVAILABLE = 'available',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  FAILED = 'failed',
  LOCKED = 'locked'
}

export enum QuestType {
  MAIN = 'main',
  SIDE = 'side',
  RANDOM = 'random',
  SURVIVAL = 'survival'
}

export enum EventType {
  STORY = 'story',
  COMBAT = 'combat',
  CHOICE = 'choice',
  DISCOVERY = 'discovery',
  SURVIVAL = 'survival',
  RANDOM = 'random'
}

export enum ItemType {
  CONSUMABLE = 'consumable',
  WEAPON = 'weapon',
  ARMOR = 'armor',
  TOOL = 'tool',
  RESOURCE = 'resource',
  QUEST_ITEM = 'quest_item'
}

export enum ItemRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

export enum LocationType {
  SAFE = 'safe',
  DANGEROUS = 'dangerous',
  NEUTRAL = 'neutral',
  UNKNOWN = 'unknown'
}
