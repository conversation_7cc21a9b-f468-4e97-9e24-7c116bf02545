import { IsString, IsEnum, IsN<PERSON>ber, IsOptional, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ItemType, ItemRarity } from '../enums';
import { GameStats } from '../types';

export class ItemEffect {
  @IsOptional()
  stats?: Partial<GameStats>;

  @IsOptional()
  @IsNumber()
  duration?: number; // in minutes

  @IsOptional()
  @IsString()
  description?: string;

  constructor(partial: Partial<ItemEffect> = {}) {
    Object.assign(this, partial);
  }
}

export class ItemMetadata {
  @IsOptional()
  @IsString()
  icon?: string;

  @IsOptional()
  @IsString()
  model?: string;

  @IsOptional()
  @IsString()
  sound?: string;

  @IsOptional()
  @IsNumber()
  weight?: number;

  @IsOptional()
  @IsNumber()
  durability?: number;

  @IsOptional()
  @IsNumber()
  maxDurability?: number;

  constructor(partial: Partial<ItemMetadata> = {}) {
    Object.assign(this, partial);
  }
}

export class InventoryItem {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsEnum(ItemType)
  type: ItemType;

  @IsEnum(ItemRarity)
  rarity: ItemRarity;

  @IsNumber()
  quantity: number;

  @IsNumber()
  maxStack: number;

  @IsBoolean()
  consumable: boolean;

  @IsBoolean()
  tradeable: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => ItemEffect)
  effect?: ItemEffect;

  @IsOptional()
  @ValidateNested()
  @Type(() => ItemMetadata)
  metadata?: ItemMetadata;

  @IsOptional()
  @IsNumber()
  value?: number; // base value for trading

  constructor(partial: Partial<InventoryItem> = {}) {
    Object.assign(this, partial);
  }
}

export class PlayerInventoryItem {
  @IsString()
  id: string;

  @IsString()
  itemId: string;

  @IsString()
  playerId: string;

  @IsNumber()
  quantity: number;

  @IsOptional()
  @IsNumber()
  slotIndex?: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => ItemMetadata)
  customMetadata?: ItemMetadata; // for item condition, modifications, etc.

  @IsOptional()
  acquiredAt?: Date;

  constructor(partial: Partial<PlayerInventoryItem> = {}) {
    Object.assign(this, partial);
  }
}

export class CreateInventoryItemDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsEnum(ItemType)
  type: ItemType;

  @IsEnum(ItemRarity)
  rarity: ItemRarity;

  @IsNumber()
  maxStack: number;

  @IsBoolean()
  consumable: boolean;

  @IsBoolean()
  tradeable: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => ItemEffect)
  effect?: ItemEffect;

  @IsOptional()
  @ValidateNested()
  @Type(() => ItemMetadata)
  metadata?: ItemMetadata;

  @IsOptional()
  @IsNumber()
  value?: number;
}

export class UpdateInventoryItemDto {
  @IsOptional()
  @IsNumber()
  quantity?: number;

  @IsOptional()
  @IsNumber()
  slotIndex?: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => ItemMetadata)
  customMetadata?: ItemMetadata;
}
