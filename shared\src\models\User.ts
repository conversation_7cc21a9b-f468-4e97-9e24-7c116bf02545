import { IsEmail, IsString, IsEnum, IsBoolean, IsO<PERSON>al, <PERSON><PERSON>ate, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { UserRole } from '../enums';

export class User {
  @IsString()
  id: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(3)
  @MaxLength(20)
  username: string;

  @IsString()
  @MinLength(6)
  password?: string; // Optional for responses

  @IsEnum(UserRole)
  role: UserRole;

  @IsBoolean()
  isActive: boolean;

  @IsOptional()
  @IsDate()
  lastLoginAt?: Date;

  @IsDate()
  createdAt: Date;

  @IsDate()
  updatedAt: Date;

  constructor(partial: Partial<User> = {}) {
    Object.assign(this, partial);
  }
}

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(3)
  @MaxLength(20)
  username: string;

  @IsString()
  @MinLength(6)
  password: string;

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
}

export class LoginDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  password: string;
}

export class UserProfileDto {
  @IsString()
  id: string;

  @IsEmail()
  email: string;

  @IsString()
  username: string;

  @IsEnum(UserRole)
  role: UserRole;

  @IsBoolean()
  isActive: boolean;

  @IsOptional()
  @IsDate()
  lastLoginAt?: Date;

  @IsDate()
  createdAt: Date;
}
